"""
Administrative view for managing teachers and system data.
"""
import flet as ft
import random
import string
from gui.components.layout import create_page_layout
from gui.config.language import get_text, DEFAULT_LANGUAGE
from facial_recognition_system.local_database import (
    get_all_teachers, update_teacher, delete_teacher,
    reset_teacher_password, get_database_statistics,
    get_all_subjects_with_classes, create_teacher_with_subjects,
    get_teacher_subjects, assign_subjects_to_teacher
)

def create_admin_view(page: ft.Page):
    """
    Create the administrative view with teacher management and system data access.

    Args:
        page: The Flet page object

    Returns:
        ft.View: The admin view
    """
    # Check if user is admin
    if not page.app_state.require_admin():
        # Redirect to login if not admin
        from gui.config.constants import ROUTE_START
        page.go(ROUTE_START)
        return ft.View("/admin", [ft.Text("Access Denied")])

    # Get current language
    current_language = getattr(page, 'language', DEFAULT_LANGUAGE)

    # State variables
    teachers_list = ft.Column(spacing=10)
    stats_container = ft.Container()

    def generate_random_credentials():
        """Generate random username and password"""
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
        return username, password

    def refresh_teachers_list():
        """Refresh the teachers list display"""
        teachers = get_all_teachers()
        teachers_list.controls.clear()

        if not teachers:
            teachers_list.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Icon(ft.icons.PERSON_OFF, size=48, color=ft.colors.GREY_400),
                        ft.Text(
                            get_text("no_teachers_yet", current_language),
                            size=16,
                            color=ft.colors.GREY_600,
                            text_align=ft.TextAlign.CENTER
                        ),
                        ft.Text(
                            get_text("create_first_teacher", current_language),
                            size=12,
                            color=ft.colors.GREY_500,
                            text_align=ft.TextAlign.CENTER
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=ft.padding.all(40),
                    alignment=ft.alignment.center
                )
            )
        else:
            for teacher in teachers:
                teacher_card = create_teacher_card(teacher, current_language, page)
                teachers_list.controls.append(teacher_card)

        page.update()

    def create_teacher_card(teacher, language, page):
        """Create a teacher card with actions"""
        def edit_teacher_click(_):
            show_edit_teacher_dialog(teacher, language, page)

        def delete_teacher_click(_):
            show_delete_teacher_dialog(teacher, language, page)

        def reset_password_click(_):
            show_reset_password_dialog(teacher, language, page)

        # Get teacher's subjects
        teacher_subjects = get_teacher_subjects(teacher['id'])
        subjects_text = ""
        if teacher_subjects:
            subject_names = [f"{s['name']} ({s['class_name']})" for s in teacher_subjects]
            if len(subject_names) > 3:
                subjects_text = f"{', '.join(subject_names[:3])}... (+{len(subject_names)-3})"
            else:
                subjects_text = ', '.join(subject_names)
        else:
            subjects_text = "Aucune matière assignée"

        return ft.Container(
            content=ft.Row([
                ft.Container(
                    content=ft.Icon(ft.icons.PERSON, color=ft.colors.BLUE_600, size=24),
                    bgcolor=ft.colors.BLUE_50,
                    padding=ft.padding.all(12),
                    border_radius=8,
                ),
                ft.Column([
                    ft.Text(teacher['name'], size=16, weight=ft.FontWeight.BOLD),
                    ft.Text(f"{get_text('teacher_username', language)}: {teacher['username']}",
                           size=12, color=ft.colors.GREY_600),
                    ft.Text(f"{get_text('teacher_subjects', language)}: {subjects_text}",
                           size=11, color=ft.colors.GREY_500),
                    ft.Text(f"Créé le: {teacher['created_at'][:10]}",
                           size=10, color=ft.colors.GREY_400)
                ], spacing=2, expand=True),
                ft.Row([
                    ft.IconButton(
                        icon=ft.icons.EDIT,
                        tooltip=get_text("edit_teacher", language),
                        on_click=edit_teacher_click,
                        icon_color=ft.colors.BLUE_600
                    ),
                    ft.IconButton(
                        icon=ft.icons.LOCK_RESET,
                        tooltip=get_text("reset_password", language),
                        on_click=reset_password_click,
                        icon_color=ft.colors.ORANGE_600
                    ),
                    ft.IconButton(
                        icon=ft.icons.DELETE,
                        tooltip=get_text("delete_teacher", language),
                        on_click=delete_teacher_click,
                        icon_color=ft.colors.RED_600
                    )
                ], spacing=0)
            ], spacing=12),
            padding=ft.padding.all(16),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )

    def refresh_statistics():
        """Refresh the statistics display"""
        stats = get_database_statistics()

        stats_cards = [
            create_stat_card(get_text("total_teachers", current_language), stats['teachers'], ft.colors.BLUE_600, ft.icons.PERSON),
            create_stat_card(get_text("total_classes", current_language), stats['classes'], ft.colors.GREEN_600, ft.icons.CLASS_),
            create_stat_card(get_text("total_students", current_language), stats['students'], ft.colors.ORANGE_600, ft.icons.SCHOOL),
            create_stat_card(get_text("total_subjects", current_language), stats['subjects'], ft.colors.PURPLE_600, ft.icons.BOOK),
            create_stat_card(get_text("total_quizzes", current_language), stats['quizzes'], ft.colors.PINK_600, ft.icons.QUIZ),
            create_stat_card(get_text("total_attendance_records", current_language), stats['attendance_records'], ft.colors.TEAL_600, ft.icons.FACT_CHECK)
        ]

        stats_container.content = ft.Row(
            stats_cards,
            wrap=True,
            spacing=16,
            run_spacing=16,
            alignment=ft.MainAxisAlignment.CENTER
        )
        page.update()

    def create_stat_card(title, value, color, icon):
        """Create a statistics card"""
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Icon(icon, color=color, size=24),
                    ft.Text(str(value), size=24, weight=ft.FontWeight.BOLD, color=color)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                ft.Text(title, size=12, color=ft.colors.GREY_600, text_align=ft.TextAlign.CENTER)
            ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            width=160,
            height=80,
            padding=ft.padding.all(16),
            bgcolor=ft.colors.WHITE,
            border_radius=12,
            border=ft.border.all(1, ft.colors.GREY_200),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )

    # Teacher form fields
    teacher_name_field = ft.TextField(
        label=get_text("teacher_name", current_language),
        hint_text=get_text("enter_teacher_name", current_language),
        width=300,
        border_radius=12,
        prefix_icon=ft.icons.PERSON_OUTLINE
    )

    generated_username_field = ft.TextField(
        label=get_text("generated_username", current_language),
        width=300,
        border_radius=12,
        read_only=True,
        prefix_icon=ft.icons.ACCOUNT_CIRCLE
    )

    generated_password_field = ft.TextField(
        label=get_text("generated_password", current_language),
        width=300,
        border_radius=12,
        read_only=True,
        password=True,
        can_reveal_password=True,
        prefix_icon=ft.icons.LOCK_OUTLINE
    )

    # Subject selection
    subjects_checkboxes = ft.Column(spacing=8)

    def refresh_subjects_list():
        """Refresh the subjects list for selection"""
        subjects = get_all_subjects_with_classes()
        subjects_checkboxes.controls.clear()

        if not subjects:
            subjects_checkboxes.controls.append(
                ft.Text(
                    get_text("no_subjects_available", current_language),
                    color=ft.colors.GREY_600,
                    size=12
                )
            )
        else:
            # Group subjects by class
            subjects_by_class = {}
            for subject in subjects:
                class_name = subject['class_name'] or "Sans classe"
                if class_name not in subjects_by_class:
                    subjects_by_class[class_name] = []
                subjects_by_class[class_name].append(subject)

            for class_name, class_subjects in subjects_by_class.items():
                # Add class header
                subjects_checkboxes.controls.append(
                    ft.Text(
                        class_name,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.BLUE_600,
                        size=14
                    )
                )

                # Add subjects for this class
                for subject in class_subjects:
                    checkbox = ft.Checkbox(
                        label=subject['name'],
                        value=False,
                        data=subject['id']  # Store subject ID in data
                    )
                    subjects_checkboxes.controls.append(
                        ft.Container(
                            content=checkbox,
                            padding=ft.padding.only(left=20)
                        )
                    )

        page.update()

    # Initialize subjects list
    refresh_subjects_list()

    def generate_credentials_click(_):
        """Generate new credentials"""
        username, password = generate_random_credentials()
        generated_username_field.value = username
        generated_password_field.value = password
        page.update()

    def add_teacher_click(_):
        """Add a new teacher"""
        if not teacher_name_field.value:
            teacher_name_field.error_text = get_text("please_enter_teacher_name", current_language)
            page.update()
            return

        if not generated_username_field.value or not generated_password_field.value:
            page.show_snack_bar(
                ft.SnackBar(
                    content=ft.Text(get_text("generate_credentials", current_language)),
                    bgcolor=ft.colors.ORANGE_600
                )
            )
            return

        # Get selected subjects
        selected_subject_ids = []
        for control in subjects_checkboxes.controls:
            if isinstance(control, ft.Container) and isinstance(control.content, ft.Checkbox):
                checkbox = control.content
                if checkbox.value and checkbox.data:
                    selected_subject_ids.append(checkbox.data)

        # Create teacher with subjects
        teacher_id = create_teacher_with_subjects(
            teacher_name_field.value,
            generated_username_field.value,
            generated_password_field.value,
            selected_subject_ids
        )

        if teacher_id:
            # Clear form
            teacher_name_field.value = ""
            teacher_name_field.error_text = None
            generated_username_field.value = ""
            generated_password_field.value = ""

            # Clear subject selections
            for control in subjects_checkboxes.controls:
                if isinstance(control, ft.Container) and isinstance(control.content, ft.Checkbox):
                    control.content.value = False

            # Show success message
            subject_count = len(selected_subject_ids)
            success_msg = f"{get_text('teacher_added_successfully', current_language)}"
            if subject_count > 0:
                success_msg += f" ({subject_count} {get_text('subjects_assigned', current_language).lower()})"

            page.show_snack_bar(
                ft.SnackBar(
                    content=ft.Text(success_msg),
                    bgcolor=ft.colors.GREEN_600
                )
            )

            # Refresh lists
            refresh_teachers_list()
            refresh_statistics()
        else:
            page.show_snack_bar(
                ft.SnackBar(
                    content=ft.Text(get_text("failed_to_add_teacher", current_language)),
                    bgcolor=ft.colors.RED_600
                )
            )

    # Generate initial credentials
    generate_credentials_click(None)

    # Initialize data
    refresh_teachers_list()
    refresh_statistics()

    # Create the admin content
    admin_content = ft.Container(
        content=ft.Column([
            # Header section with logout
            ft.Container(
                content=ft.Column([
                    # Admin header with logout button
                    ft.Row([
                        ft.Row([
                            ft.Icon(ft.icons.ADMIN_PANEL_SETTINGS, color=ft.colors.BLUE_600, size=28),
                            ft.Text(
                                get_text("admin_panel", current_language),
                                size=24,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.BLUE_900
                            )
                        ], spacing=12),
                        ft.Row([
                            ft.Text(
                                f"Admin: {page.app_state.current_user['name']}",
                                size=14,
                                color=ft.colors.GREY_600
                            ),
                            ft.ElevatedButton(
                                content=ft.Row([
                                    ft.Icon(ft.icons.LOGOUT, size=16),
                                    ft.Text("Déconnexion", size=12)
                                ], spacing=4, tight=True),
                                on_click=lambda _: logout_admin(),
                                style=ft.ButtonStyle(
                                    bgcolor=ft.colors.RED_600,
                                    color=ft.colors.WHITE,
                                    shape=ft.RoundedRectangleBorder(radius=8)
                                ),
                                height=36
                            )
                        ], spacing=12)
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    ft.Container(height=8),
                    ft.Text(
                        get_text("admin_description", current_language),
                        size=14,
                        color=ft.colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
            ),

            # Statistics section
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        ft.Icon(ft.icons.ANALYTICS, color=ft.colors.BLUE_600, size=20),
                        ft.Text(
                            get_text("database_statistics", current_language),
                            size=18,
                            weight=ft.FontWeight.W_600,
                            color=ft.colors.GREY_800
                        )
                    ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),
                    stats_container
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(24),
                margin=ft.margin.only(bottom=20),
                bgcolor=ft.colors.WHITE,
                border_radius=16,
                width=800,
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=10,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                ),
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=0, scroll=ft.ScrollMode.AUTO),
        alignment=ft.alignment.center,
        expand=True
    )

    # Teacher management section
    teacher_management_section = ft.Container(
        content=ft.Column([
            ft.Row([
                ft.Icon(ft.icons.PEOPLE, color=ft.colors.BLUE_600, size=20),
                ft.Text(
                    get_text("teacher_management", current_language),
                    size=18,
                    weight=ft.FontWeight.W_600,
                    color=ft.colors.GREY_800
                )
            ], spacing=8, alignment=ft.MainAxisAlignment.CENTER),
            ft.Container(height=16),

            # Add teacher form
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        get_text("add_new_teacher", current_language),
                        size=16,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.GREY_700
                    ),
                    ft.Container(height=12),
                    teacher_name_field,
                    ft.Container(height=12),
                    ft.Row([
                        generated_username_field,
                        ft.Container(width=12),
                        generated_password_field
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    ft.Container(height=16),

                    # Subject selection section
                    ft.Text(
                        get_text("assign_subjects", current_language),
                        size=14,
                        weight=ft.FontWeight.W_500,
                        color=ft.colors.GREY_700
                    ),
                    ft.Container(height=8),
                    ft.Container(
                        content=ft.Column([
                            ft.Text(
                                get_text("select_subjects", current_language),
                                size=12,
                                color=ft.colors.GREY_600
                            ),
                            ft.Container(height=8),
                            ft.Container(
                                content=subjects_checkboxes,
                                height=200,
                                border=ft.border.all(1, ft.colors.GREY_300),
                                border_radius=8,
                                padding=ft.padding.all(12),
                                bgcolor=ft.colors.GREY_50
                            )
                        ], spacing=0),
                        width=620
                    ),
                    ft.Container(height=16),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.REFRESH, size=18),
                                ft.Text(get_text("generate_credentials", current_language), size=14)
                            ], spacing=8, tight=True),
                            on_click=generate_credentials_click,
                            style=ft.ButtonStyle(
                                bgcolor=ft.colors.ORANGE_600,
                                color=ft.colors.WHITE,
                                shape=ft.RoundedRectangleBorder(radius=12)
                            )
                        ),
                        ft.Container(width=12),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.PERSON_ADD, size=18),
                                ft.Text(get_text("add_teacher", current_language), size=14)
                            ], spacing=8, tight=True),
                            on_click=add_teacher_click,
                            style=ft.ButtonStyle(
                                bgcolor=ft.colors.BLUE_600,
                                color=ft.colors.WHITE,
                                shape=ft.RoundedRectangleBorder(radius=12)
                            )
                        )
                    ], alignment=ft.MainAxisAlignment.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(20),
                bgcolor=ft.colors.BLUE_50,
                border_radius=12,
                border=ft.border.all(1, ft.colors.BLUE_200)
            ),

            ft.Container(height=24),

            # Teachers list
            ft.Text(
                get_text("existing_teachers", current_language),
                size=16,
                weight=ft.FontWeight.W_500,
                color=ft.colors.GREY_700
            ),
            ft.Container(height=12),
            teachers_list
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(24),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.colors.WHITE,
        border_radius=16,
        width=800,
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
            offset=ft.Offset(0, 2)
        ),
    )

    # Add teacher management section to admin content
    admin_content.content.controls.append(teacher_management_section)

    def show_edit_teacher_dialog(teacher, language, page):
        """Show edit teacher dialog"""
        name_field = ft.TextField(
            label=get_text("teacher_name", language),
            value=teacher['name'],
            width=300
        )
        username_field = ft.TextField(
            label=get_text("teacher_username", language),
            value=teacher['username'],
            width=300
        )

        def save_changes(_):
            if not name_field.value or not username_field.value:
                return

            success = update_teacher(teacher['id'], name_field.value, username_field.value)
            if success:
                page.show_snack_bar(
                    ft.SnackBar(
                        content=ft.Text(get_text("teacher_updated_successfully", language)),
                        bgcolor=ft.colors.GREEN_600
                    )
                )
                refresh_teachers_list()
            else:
                page.show_snack_bar(
                    ft.SnackBar(
                        content=ft.Text(get_text("failed_to_update_teacher", language)),
                        bgcolor=ft.colors.RED_600
                    )
                )
            page.dialog.open = False
            page.update()

        page.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(get_text("edit_teacher", language)),
            content=ft.Column([name_field, username_field], tight=True),
            actions=[
                ft.TextButton(get_text("cancel", language), on_click=lambda _: close_dialog()),
                ft.ElevatedButton(get_text("save", language), on_click=save_changes)
            ]
        )
        page.dialog.open = True
        page.update()

    def show_delete_teacher_dialog(teacher, language, page):
        """Show delete teacher confirmation dialog"""
        def confirm_delete(_):
            success = delete_teacher(teacher['id'])
            if success:
                page.show_snack_bar(
                    ft.SnackBar(
                        content=ft.Text(get_text("teacher_deleted_successfully", language)),
                        bgcolor=ft.colors.GREEN_600
                    )
                )
                refresh_teachers_list()
                refresh_statistics()
            else:
                page.show_snack_bar(
                    ft.SnackBar(
                        content=ft.Text(get_text("failed_to_delete_teacher", language)),
                        bgcolor=ft.colors.RED_600
                    )
                )
            page.dialog.open = False
            page.update()

        page.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(get_text("delete_teacher", language)),
            content=ft.Text(get_text("delete_teacher_confirmation", language)),
            actions=[
                ft.TextButton(get_text("cancel", language), on_click=lambda _: close_dialog()),
                ft.ElevatedButton(
                    get_text("delete", language),
                    on_click=confirm_delete,
                    style=ft.ButtonStyle(bgcolor=ft.colors.RED_600, color=ft.colors.WHITE)
                )
            ]
        )
        page.dialog.open = True
        page.update()

    def show_reset_password_dialog(teacher, language, page):
        """Show reset password dialog"""
        new_password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))

        password_field = ft.TextField(
            label=get_text("new_password", language),
            value=new_password,
            width=300,
            password=True,
            can_reveal_password=True
        )

        def reset_password(_):
            success = reset_teacher_password(teacher['id'], password_field.value)
            if success:
                page.show_snack_bar(
                    ft.SnackBar(
                        content=ft.Text(get_text("password_reset_successfully", language)),
                        bgcolor=ft.colors.GREEN_600
                    )
                )
            else:
                page.show_snack_bar(
                    ft.SnackBar(
                        content=ft.Text(get_text("failed_to_reset_password", language)),
                        bgcolor=ft.colors.RED_600
                    )
                )
            page.dialog.open = False
            page.update()

        page.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(get_text("reset_password", language)),
            content=ft.Column([
                ft.Text(f"Nouveau mot de passe pour {teacher['name']}:"),
                password_field
            ], tight=True),
            actions=[
                ft.TextButton(get_text("cancel", language), on_click=lambda _: close_dialog()),
                ft.ElevatedButton(get_text("reset_password", language), on_click=reset_password)
            ]
        )
        page.dialog.open = True
        page.update()

    def close_dialog():
        """Close the current dialog"""
        page.dialog.open = False
        page.update()

    def logout_admin():
        """Logout admin and redirect to login"""
        page.app_state.logout()
        from gui.config.constants import ROUTE_START
        page.go(ROUTE_START)

    return create_page_layout(
        page,
        "",  # No title since we have custom header
        admin_content
    )
