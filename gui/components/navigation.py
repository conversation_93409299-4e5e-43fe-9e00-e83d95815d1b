"""
Navigation components for the application.
"""
import flet as ft
from gui.config.constants import (
    ROUTE_DASHBOARD, ROUTE_CLASSES, ROUTE_SUBJECTS, ROUTE_QUIZZES, ROUTE_ADMIN,
    ICON_DASHBOARD, ICON_CLASS, ICON_SUBJECT, ICON_QUIZ, ICON_ADMIN
)
from gui.config.language import get_text
from gui.services.dashboard_service import get_dashboard_statistics

def create_navigation_rail(page: ft.Page):
    """
    Create the navigation rail component for the application.

    Args:
        page: The Flet page object

    Returns:
        ft.NavigationRail: The navigation rail component
    """
    # For mobile, we'll use a drawer instead of a rail
    if getattr(page, 'is_mobile', False):
        return ft.Container(width=0)  # Return an empty container

    # Get current language and statistics
    current_language = getattr(page, 'language', 'en')
    stats = get_dashboard_statistics()

    # Create enhanced navigation rail with better styling
    return ft.Container(
        content=ft.Column([
            # Navigation items with enhanced design
            create_enhanced_nav_item(
                ICON_DASHBOARD,
                get_text("dashboard", current_language),
                get_text("overview", current_language),
                ROUTE_DASHBOARD,
                page.route == ROUTE_DASHBOARD,
                page,
                ft.Colors.BLUE_600
            ),

            create_enhanced_nav_item(
                ICON_CLASS,
                get_text("classes", current_language),
                f"{stats['classes_count']} {get_text('classes_count', current_language)}",
                ROUTE_CLASSES,
                page.route == ROUTE_CLASSES,
                page,
                ft.Colors.GREEN_600
            ),

            create_enhanced_nav_item(
                ICON_SUBJECT,
                get_text("subjects", current_language),
                f"{stats['subjects_count']} {get_text('subjects_available', current_language)}",
                ROUTE_SUBJECTS,
                page.route == ROUTE_SUBJECTS,
                page,
                ft.Colors.ORANGE_600
            ),

            create_enhanced_nav_item(
                ICON_QUIZ,
                get_text("quizzes", current_language),
                f"{stats['quizzes_count']} {get_text('quizzes_created', current_language)}",
                ROUTE_QUIZZES,
                page.route == ROUTE_QUIZZES,
                page,
                ft.Colors.PURPLE_600
            ),

            create_enhanced_nav_item(
                ICON_ADMIN,
                get_text("admin", current_language),
                get_text("system_overview", current_language),
                ROUTE_ADMIN,
                page.route == ROUTE_ADMIN,
                page,
                ft.Colors.RED_600
            )
        ], spacing=8, scroll=ft.ScrollMode.AUTO),
        width=200,
        bgcolor=ft.Colors.SURFACE,
        border=ft.border.only(right=ft.border.BorderSide(1, ft.Colors.OUTLINE)),
        padding=ft.padding.symmetric(vertical=8)
    )

def create_enhanced_nav_item(icon, title, subtitle, route, is_selected, page, color):
    """Create an enhanced navigation item with better visual design."""
    return ft.Container(
        content=ft.Row([
            ft.Container(
                content=ft.Icon(icon, size=20,
                               color=ft.Colors.WHITE if is_selected else color),
                bgcolor=color if is_selected else ft.Colors.TRANSPARENT,
                padding=ft.padding.all(8),
                border_radius=ft.border_radius.all(8),
                width=36,
                height=36
            ),
            ft.Column([
                ft.Text(title, size=13, weight=ft.FontWeight.BOLD,
                       color=color if is_selected else ft.Colors.ON_SURFACE),
                ft.Text(subtitle, size=10, color=ft.Colors.GREY_600)
            ], spacing=2, expand=True)
        ], spacing=12),
        padding=ft.padding.symmetric(horizontal=12, vertical=8),
        margin=ft.margin.symmetric(horizontal=8, vertical=2),
        bgcolor=ft.Colors.with_opacity(0.1, color) if is_selected else ft.Colors.TRANSPARENT,
        border_radius=ft.border_radius.all(12),
        on_click=lambda _: navigate_to_route(page, route),
        border=ft.border.all(1, ft.Colors.with_opacity(0.3, color)) if is_selected else None
    )

def create_mobile_nav_item(icon, title, subtitle, route, is_selected, page, color):
    """Create an enhanced mobile navigation item."""
    return ft.Container(
        content=ft.Row([
            ft.Container(
                content=ft.Icon(icon, size=22,
                               color=ft.Colors.WHITE if is_selected else color),
                bgcolor=color if is_selected else ft.Colors.with_opacity(0.1, color),
                padding=ft.padding.all(10),
                border_radius=ft.border_radius.all(10),
                width=44,
                height=44
            ),
            ft.Column([
                ft.Text(title, size=14, weight=ft.FontWeight.BOLD,
                       color=color if is_selected else ft.Colors.ON_SURFACE),
                ft.Text(subtitle, size=11, color=ft.Colors.GREY_600)
            ], spacing=2, expand=True)
        ], spacing=16),
        padding=ft.padding.all(12),
        margin=ft.margin.symmetric(horizontal=8, vertical=4),
        bgcolor=ft.Colors.with_opacity(0.08, color) if is_selected else ft.Colors.TRANSPARENT,
        border_radius=ft.border_radius.all(12),
        on_click=lambda _: handle_drawer_item_click(page, route),
        border=ft.border.all(1, ft.Colors.with_opacity(0.2, color)) if is_selected else None
    )

def create_navigation_drawer(page: ft.Page):
    """
    Create a custom navigation drawer for mobile devices using standard components.

    Args:
        page: The Flet page object

    Returns:
        ft.Container: A custom drawer container
    """
    # Get current language and statistics
    current_language = getattr(page, 'language', 'en')
    stats = get_dashboard_statistics()

    # Create enhanced navigation items for mobile
    nav_items = [
        # Enhanced navigation items with statistics
        create_mobile_nav_item(
            ICON_DASHBOARD,
            get_text("dashboard", current_language),
            get_text("overview_stats", current_language),
            ROUTE_DASHBOARD,
            page.route == ROUTE_DASHBOARD,
            page,
            ft.Colors.BLUE_600
        ),

        create_mobile_nav_item(
            ICON_CLASS,
            get_text("classes", current_language),
            f"{stats['classes_count']} {get_text('classes_count', current_language)} • {stats['students_count']} {get_text('students_count', current_language)}",
            ROUTE_CLASSES,
            page.route == ROUTE_CLASSES,
            page,
            ft.Colors.GREEN_600
        ),

        create_mobile_nav_item(
            ICON_SUBJECT,
            get_text("subjects", current_language),
            f"{stats['subjects_count']} {get_text('subjects_available', current_language)}",
            ROUTE_SUBJECTS,
            page.route == ROUTE_SUBJECTS,
            page,
            ft.Colors.ORANGE_600
        ),

        create_mobile_nav_item(
            ICON_QUIZ,
            get_text("quizzes", current_language),
            f"{stats['quizzes_count']} {get_text('quizzes_created', current_language)}",
            ROUTE_QUIZZES,
            page.route == ROUTE_QUIZZES,
            page,
            ft.Colors.PURPLE_600
        ),

        create_mobile_nav_item(
            ICON_ADMIN,
            get_text("admin", current_language),
            get_text("system_overview", current_language),
            ROUTE_ADMIN,
            page.route == ROUTE_ADMIN,
            page,
            ft.Colors.RED_600
        )
    ]

    # Create a custom drawer using Container
    # Use a safe height value that works on all platforms
    safe_height = 800  # Default height if window_height is not available
    try:
        if hasattr(page, 'window_height') and page.window_height:
            safe_height = page.window_height
    except Exception:
        pass

    return ft.Container(
        content=ft.Column(nav_items, scroll=ft.ScrollMode.AUTO),
        bgcolor=ft.Colors.SURFACE,
        width=250,
        height=safe_height,
        padding=ft.padding.all(0),
        border=ft.border.only(right=ft.border.BorderSide(1, ft.Colors.OUTLINE)),
        left=0,  # Position at the left edge
        top=0,   # Position at the top
        # Add shadow for better visibility
        shadow=ft.BoxShadow(
            spread_radius=1,
            blur_radius=15,
            color=ft.Colors.with_opacity(0.25, ft.Colors.BLACK),
            offset=ft.Offset(2, 0)
        ),
        visible=False,  # Start hidden
    )

def navigate_to_route(page, route):
    """
    Navigate to a route while storing the previous route for settings navigation.

    Args:
        page: The Flet page object
        route: The route to navigate to
    """
    # Store current route as previous route (for settings navigation)
    if page.route != route and page.route != '/settings':
        page._previous_route = page.route

    page.go(route)

def handle_drawer_item_click(page, route):
    """
    Handle navigation drawer item click events.

    Args:
        page: The Flet page object
        route: The route to navigate to
    """
    # Close the drawer
    from gui.components.app_bar import toggle_drawer
    toggle_drawer(page)  # This will handle both drawer and overlay

    # Navigate to the selected route
    navigate_to_route(page, route)

def get_nav_index(route):
    """
    Get the navigation index based on the current route.

    Args:
        route: The current route

    Returns:
        int: The navigation index
    """
    routes = {
        ROUTE_DASHBOARD: 0,
        ROUTE_CLASSES: 1,
        ROUTE_SUBJECTS: 2,
        ROUTE_QUIZZES: 3,
        ROUTE_ADMIN: 4,
    }
    return routes.get(route, 0)

def handle_nav_change(e, page):
    """
    Handle navigation change events.

    Args:
        e: The navigation change event
        page: The Flet page object
    """
    routes = [ROUTE_DASHBOARD, ROUTE_CLASSES, ROUTE_SUBJECTS, ROUTE_QUIZZES, ROUTE_ADMIN]
    page.go(routes[e.control.selected_index])
